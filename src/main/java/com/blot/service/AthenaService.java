package com.blot.service;

import com.blot.constants.SQLContants;
import com.blot.controller.dto.QueryRequestDTO;
import com.blot.enums.QueryType;
import com.blot.security.TokenValidator;
import com.blot.util.AthenaUtil;
import com.blot.util.QueryValidator;
import com.blot.util.SqlQueryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.athena.AthenaClient;
import software.amazon.awssdk.services.athena.model.*;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AthenaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AthenaService.class);

    @Autowired
    private AthenaUtil athenaUtil;

    @Autowired
    private AthenaClient amazonAthenaClient;

    @Autowired
    private SqlQueryBuilder sqlQueryBuilder;

    @Autowired
    private TokenValidator tokenValidator;

    @Tool(name = "run_query", description = "Execute an AWS Athena query with a given schema and query string. Requires a valid JWT token.")
    public Map<String, Object> runQuery(String schema, String query, String token) {
        LOGGER.debug("Running Athena query on schema: {}\nQuery: {}", schema, query);
        Map<String, Object> response = new HashMap<>();
        try {
            tokenValidator.validate(token);
            QueryValidator.validateQuery(query);
            String sanitizedSchema = QueryValidator.sanitizeIdentifier(schema);
            String queryExecutionId = athenaUtil.submitAthenaQuery(amazonAthenaClient, sanitizedSchema, query);
            LOGGER.debug("Submitted Athena query. Execution ID: {}", queryExecutionId);
            athenaUtil.waitForQueryToComplete(amazonAthenaClient, queryExecutionId);
            // Fetch query status using existing getStatus method
            String status = getStatus(queryExecutionId, token);
            List<String> results = getQueryResults(queryExecutionId, true);
            response.put("queryExecutionId", queryExecutionId);
            response.put("status", status);
            response.put("results", results);
        } catch (AthenaException | IllegalArgumentException | InterruptedException e) {
            LOGGER.error("Error running Athena query", e);
            response.put("error", "[ERROR] Query failed: " + e.getMessage());
        }
        return response;
    }

    @Tool(name = "describe_table", description = "Get column names and data types for a given schema and table in Athena. Requires a valid JWT token.")
    public List<String> describeTable(String schema, String table, String token) {
        try {
            tokenValidator.validate(token);
            String sanitizedSchema = QueryValidator.sanitizeIdentifier(schema);
            String sanitizedTable = QueryValidator.sanitizeIdentifier(table);
            String query = String.format("DESCRIBE %s.%s", sanitizedSchema, sanitizedTable);
            LOGGER.debug("Describing table: {}.{}", sanitizedSchema, sanitizedTable);
            String queryExecutionId = athenaUtil.submitAthenaQuery(amazonAthenaClient, sanitizedSchema, query);
            athenaUtil.waitForQueryToComplete(amazonAthenaClient, queryExecutionId);
            return getQueryResults(queryExecutionId, false);
        } catch (AthenaException | IllegalArgumentException | InterruptedException e) {
            LOGGER.error("Error describing Athena table", e);
            return List.of("[ERROR] Describe table failed: " + e.getMessage());
        }
    }

    @Tool(name = "list_tables", description = "List all tables in the given Athena schema. Requires a valid JWT token.")
    public List<String> listTables(String schema, String token) {
        String sanitizedSchema;
        try {
            tokenValidator.validate(token);
            sanitizedSchema = QueryValidator.sanitizeIdentifier(schema);
            String query = String.format("SHOW TABLES IN %s", sanitizedSchema);
            LOGGER.debug("Listing tables in schema: {}", sanitizedSchema);
            String queryExecutionId = athenaUtil.submitAthenaQuery(amazonAthenaClient, sanitizedSchema, query);
            athenaUtil.waitForQueryToComplete(amazonAthenaClient, queryExecutionId);
            return getQueryResults(queryExecutionId, true);
        } catch (AthenaException | IllegalArgumentException | InterruptedException e) {
            LOGGER.error("Error listing tables in schema: {}", schema, e);
            return List.of("[ERROR] List tables failed: " + e.getMessage());
        }
    }

    @Tool(name = "get_status", description = "Get the status of an Athena query using queryExecutionId. Requires a valid JWT token.")
    public String getStatus(String queryExecutionId, String token) {
        try {
            tokenValidator.validate(token);
            GetQueryExecutionRequest request = GetQueryExecutionRequest.builder()
                    .queryExecutionId(queryExecutionId)
                    .build();
            GetQueryExecutionResponse response = amazonAthenaClient.getQueryExecution(request);
            QueryExecutionState state = response.queryExecution().status().state();
            LOGGER.debug("QueryExecutionId {} is in state: {}", queryExecutionId, state);
            return state.toString();
        } catch (AthenaException e) {
            LOGGER.error("Error fetching query status for ID: {}", queryExecutionId, e);
            return "[ERROR] Fetching query status: " + e.getMessage();
        }
    }

    @Tool(name = "get_result", description = "Get result rows for a given query execution ID. Returns 50 rows by default. Requires a valid JWT token.")
    public List<String> getResult(String queryExecutionId, Integer numberOfRows, String token) {
        try {
            tokenValidator.validate(token);
            int rowsToFetch = (numberOfRows != null) ? numberOfRows : 50;
            GetQueryResultsRequest resultsRequest = GetQueryResultsRequest.builder()
                    .queryExecutionId(queryExecutionId)
                    .maxResults(rowsToFetch)
                    .build();
            GetQueryResultsResponse resultsResponse = amazonAthenaClient.getQueryResults(resultsRequest);
            List<Row> rows = resultsResponse.resultSet().rows();
            return rows.stream()
                    .skip(1)
                    .limit(rowsToFetch)
                    .map(row -> row.data().stream()
                            .map(d -> d.varCharValue() != null ? d.varCharValue() : "")
                            .collect(Collectors.joining(", "))
                    )
                    .collect(Collectors.toList());
        } catch (AthenaException e) {
            LOGGER.error("Error fetching Athena query results", e);
            return List.of("[ERROR] Failed to fetch results: " + e.getMessage());
        }
    }

    @Tool(name = "get_query", description = "Generate a funnel or attribution query for Athena based on provided parameters. Requires a valid JWT token.")
    public String getQuery(String appName, String queryType, String schema, String events, String attributionType,
                           String fromDate, String toDate, String token) {
        try {
            tokenValidator.validate(token);

            String appQuery = SqlQueryBuilder.generateQuery(QueryType.APP_ID, null, appName);
            LOGGER.debug("Running Athena query on schema: {}\nQuery: {}", schema, appQuery);

            String queryExecutionId = athenaUtil.submitAthenaQuery(amazonAthenaClient, schema, appQuery);
            athenaUtil.waitForQueryToComplete(amazonAthenaClient, queryExecutionId);

            Map<String, String> resultMap = athenaUtil.processAthenaQuery(amazonAthenaClient, queryExecutionId);
            String appId = resultMap.get(SQLContants.APPLICATION_ID_COLUMN_NAME);

            QueryRequestDTO queryRequestDTO = buildQueryRequest(queryType, fromDate, toDate, events, attributionType, appId);
            if (queryRequestDTO == null) {
                return "[ERROR] Invalid input parameters for query generation.";
            }

            QueryType qt = QueryType.valueOf(queryType.toUpperCase());
            String query = SqlQueryBuilder.generateQuery(qt, queryRequestDTO, appName);

            return "Generated query:\n\n" + query + "\n\nTo execute, call `run_query` with schema='" + schema + "' and the query.";
        } catch (AthenaException e) {
            LOGGER.error("Athena error for appName={}: {}", appName, e.getMessage(), e);
            return "[ERROR] Athena error: " + e.getMessage();
        } catch (Exception e) {
            LOGGER.error("General error for appName={}: {}", appName, e.getMessage(), e);
            return "[ERROR] Unexpected error: " + e.getMessage();
        }
    }

    private List<String> getQueryResults(String queryExecutionId, boolean skipHeader) {
        GetQueryResultsRequest resultsRequest = GetQueryResultsRequest.builder()
                .queryExecutionId(queryExecutionId)
                .build();
        GetQueryResultsResponse resultsResponse = amazonAthenaClient.getQueryResults(resultsRequest);
        List<Row> rows = resultsResponse.resultSet().rows();
        return rows.stream()
                .skip(skipHeader ? 1 : 0)
                .map(row -> row.data().stream()
                        .map(datum -> datum.varCharValue() != null ? datum.varCharValue() : "")
                        .collect(Collectors.joining(" : "))
                )
                .collect(Collectors.toList());
    }

    private QueryRequestDTO buildQueryRequest(String queryType, String fromDate, String toDate,
                                              String events, String attributionType, String appId) {
        if ("funnel".equalsIgnoreCase(queryType)) {
            if (events == null || events.isBlank()) {
                LOGGER.warn("Missing 'events' parameter for funnel query.");
                return null;
            }
            List<String> eventList = Arrays.stream(events.split(","))
                    .map(String::trim)
                    .filter(e -> !e.isEmpty())
                    .toList();
            return new QueryRequestDTO(fromDate, toDate, null, eventList, appId);
        } else if ("attribution".equalsIgnoreCase(queryType)) {
            if (fromDate == null || toDate == null || attributionType == null) {
                LOGGER.warn("Missing required parameters for attribution query.");
                return null;
            }
            return new QueryRequestDTO(fromDate, toDate, attributionType, null, appId);
        }
        LOGGER.warn("Unsupported query type: {}", queryType);
        return null;
    }
}
