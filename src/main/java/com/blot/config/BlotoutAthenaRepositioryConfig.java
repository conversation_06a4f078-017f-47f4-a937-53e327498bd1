package com.blot.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.athena.AthenaClient;

import java.time.Duration;

@Configuration
public class BlotoutAthenaRepositioryConfig {

	public static final long SLEEP_AMOUNT_IN_MS = 1000;

	@Value("${datasource.athena.timeout}")
	private int timeout;

	@Value("${datasource.athena.region}")
	private String region;

	@Value("${datasource.athena.access-key}")
	private String awsAccessKey;

	@Value("${datasource.athena.secret-key}")
	private String awsSecretKey;

	@Bean
	public AthenaClient createClient() {
		AwsBasicCredentials awsCreds = AwsBasicCredentials.create(awsAccessKey, awsSecretKey);

		ClientOverrideConfiguration clientConfig = ClientOverrideConfiguration.builder()
				.apiCallTimeout(Duration.ofMillis(timeout))
				.build();

		return AthenaClient.builder()
				.region(Region.of(region))
				.credentialsProvider(StaticCredentialsProvider.create(awsCreds))
				.overrideConfiguration(clientConfig)
				.build();
	}
}
