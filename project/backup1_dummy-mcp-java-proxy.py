import os
import json
import sys
import traceback
from typing import Any
from mcp.server.fastmcp.server import FastMCP
import httpx

print("🚀 Starting MCP server...", file=sys.stderr)

# Load API token from environment or fallback
TOKEN = os.getenv("API_TOKEN", "+jawgicebv2pfrqrutltw0kw1mi2tqm7ufesmzrpsdeooobwclet5zxhftqo1d62")
NWS_API_BASE = "http://localhost:8080"

# Initialize MCP server
mcp = FastMCP(name="java-proxy")

async def make_nws_request(url: str) -> dict[str, Any] | None:
    """Make a request to your local API with error handling."""
    headers = {
        "Accept": "application/json",
        "Token": TOKEN,
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=10.0)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Request failed: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            return None

@mcp.tool()
async def get_application(input: dict[str, Any]) -> str:
    """Fetch list of applications from the local API."""
    print("📥 Tool called: get_application", file=sys.stderr)

    url = f"{NWS_API_BASE}/api/v1/application"
    print(f"🔗 Requesting URL: {url}", file=sys.stderr)
    data = await make_nws_request(url)
    if data is None:
        return "❌ Failed to fetch application data."

    apps = data.get("applications")
    if not isinstance(apps, list):
        return f"⚠️ Unexpected format: {type(apps)}"

    # Optional: Generate summary
    summaries = []
    for app in apps:
        summaries.append(f"- {app.get('name')} ({app.get('application_id')})")

    return f"✅ Found {len(apps)} applications:\n" + "\n".join(summaries)

if __name__ == "__main__":
    try:
        print("🚦 Running MCP server loop now...", file=sys.stderr)
        mcp.run(transport="stdio")
    except Exception as e:
        print(f"❌ MCP server crashed: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
