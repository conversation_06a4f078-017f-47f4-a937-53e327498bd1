package com.blot.util;

import com.blot.config.BlotoutAthenaRepositioryConfig;
import com.blot.constants.AthenaConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.services.athena.AthenaClient;
import software.amazon.awssdk.services.athena.model.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class AthenaUtil {

	private static final Logger LOGGER = LoggerFactory.getLogger(AthenaUtil.class);

	@Value("${datasource.athena.output.bucket}")
	private String outputBucket;

	public String submitAthenaQuery(AthenaClient athenaClient, String database, String query) {
		QueryExecutionContext queryExecutionContext = QueryExecutionContext.builder()
				.database(database)
				.build();

		ResultConfiguration resultConfiguration = ResultConfiguration.builder()
				.outputLocation(outputBucket)
				.build();

		StartQueryExecutionRequest startQueryExecutionRequest = StartQueryExecutionRequest.builder()
				.queryString(query)
				.queryExecutionContext(queryExecutionContext)
				.resultConfiguration(resultConfiguration)
				.build();

		StartQueryExecutionResponse startQueryExecutionResponse = athenaClient.startQueryExecution(startQueryExecutionRequest);
		return startQueryExecutionResponse.queryExecutionId();
	}

	public void waitForQueryToComplete(AthenaClient athenaClient, String queryExecutionId) throws InterruptedException {
		GetQueryExecutionRequest getQueryExecutionRequest = GetQueryExecutionRequest.builder()
				.queryExecutionId(queryExecutionId)
				.build();

		boolean isQueryStillRunning = true;
		while (isQueryStillRunning) {
			GetQueryExecutionResponse getQueryExecutionResponse = athenaClient.getQueryExecution(getQueryExecutionRequest);
			String queryState = getQueryExecutionResponse.queryExecution().status().state().toString();

			switch (queryState) {
				case "FAILED":
					throw new RuntimeException("Query Failed: " +
							getQueryExecutionResponse.queryExecution().status().stateChangeReason());
				case "CANCELLED":
					throw new RuntimeException("Query was cancelled.");
				case "SUCCEEDED":
					isQueryStillRunning = false;
					break;
				default:
					Thread.sleep(BlotoutAthenaRepositioryConfig.SLEEP_AMOUNT_IN_MS);
			}
		}
	}

	public Map<String, String> getAthenaQueryExecutionParameters(AthenaClient athenaClient, String queryExecutionId) {
		Map<String, String> athenaQueryParams = new HashMap<>();
		GetQueryExecutionRequest getQueryExecutionRequest = GetQueryExecutionRequest.builder()
				.queryExecutionId(queryExecutionId)
				.build();
		QueryExecution queryExecution = athenaClient.getQueryExecution(getQueryExecutionRequest).queryExecution();
		athenaQueryParams.put(AthenaConstants.ATHENA_QUERY, queryExecution.query());
		athenaQueryParams.put(AthenaConstants.ATHENA_RESULT_CONFIGURATION_OUTPUT_LOCATION, queryExecution.resultConfiguration().outputLocation());
		athenaQueryParams.put(AthenaConstants.ATHENA_QUERY_EXECUTION_ID, queryExecution.queryExecutionId());
		athenaQueryParams.put(AthenaConstants.ATHENA_STATUS_STATE, queryExecution.status().state().toString());
		return athenaQueryParams;
	}

	public Map<String, String> getAthenaBatchQueryExecutionParameters(AthenaClient athenaClient, List<String> queryExecutionIds) {
		Map<String, String> athenaQueryIdAndStatus = new HashMap<>();
		BatchGetQueryExecutionRequest batchGetQueryExecutionRequest = BatchGetQueryExecutionRequest.builder()
				.queryExecutionIds(queryExecutionIds)
				.build();
		BatchGetQueryExecutionResponse batchGetQueryExecutionResponse = athenaClient.batchGetQueryExecution(batchGetQueryExecutionRequest);
		batchGetQueryExecutionResponse.queryExecutions().forEach(queryExecution ->
				athenaQueryIdAndStatus.put(queryExecution.queryExecutionId(), queryExecution.status().state().toString()));
		return athenaQueryIdAndStatus;
	}

	public Map<String, String> processAthenaQuery(AthenaClient athenaClient, String queryExecutionId) {
		Map<String, String> resultMap = new HashMap<>();

		GetQueryResultsRequest request = GetQueryResultsRequest.builder()
				.queryExecutionId(queryExecutionId)
				.build();

		GetQueryResultsResponse response;

		do {
			response = athenaClient.getQueryResults(request);
			List<ColumnInfo> columnInfoList = response.resultSet().resultSetMetadata().columnInfo();
			List<Row> rows = response.resultSet().rows();

			for (int i = 1; i < rows.size(); i++) { // Skip header
				Row row = rows.get(i);
				List<Datum> data = row.data();

				for (int colIndex = 0; colIndex < columnInfoList.size(); colIndex++) {
					String columnName = columnInfoList.get(colIndex).name();
					String columnValue = data.get(colIndex).varCharValue();

					resultMap.put(columnName.toLowerCase(), columnValue); // Use lowercase keys for consistency
				}

				// For now, return first row only
				if (!resultMap.isEmpty()) {
					return resultMap;
				}
			}

			request = request.toBuilder()
					.nextToken(response.nextToken())
					.build();

		} while (response.nextToken() != null);

		return resultMap;
	}
}
