import subprocess
import json
import threading
import queue
import sys

def enqueue_output(out, q):
    for line in iter(out.readline, ''):
        q.put(line)
    out.close()

def main():
    proc = subprocess.Popen(
        ["python3", "-u", "dummy-mcp-java-proxy.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,
    )

    stdout_queue = queue.Queue()
    stderr_queue = queue.Queue()

    # Start threads to asynchronously read stdout and stderr
    threading.Thread(target=enqueue_output, args=(proc.stdout, stdout_queue), daemon=True).start()
    threading.Thread(target=enqueue_output, args=(proc.stderr, stderr_queue), daemon=True).start()

    def send(msg):
        json_str = json.dumps(msg)
        print(f"SENDING: {json_str}", file=sys.stderr)
        proc.stdin.write(json_str + "\n")
        proc.stdin.flush()

    # Step 1: Send initialize
    send({
        "jsonrpc": "2.0",
        "id": 0,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {},
            "clientInfo": {"name": "test", "version": "0.0.1"},
        },
    })

    # Step 2: Send tool call
    send({
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "calls": [{
                "tool": "get_application",
                "toolCallId": "call-1",
                "input": {},
            }]
        },
    })

    # Read responses (wait for 2 JSON-RPC responses)
    received_responses = 0
    while received_responses < 2:
        try:
            line = stdout_queue.get(timeout=5)  # wait max 5 seconds for a line
            line = line.strip()
            if line:
                print("RECEIVED:", line)
                received_responses += 1
        except queue.Empty:
            print("Timeout waiting for MCP server response", file=sys.stderr)
            break

    # Print any stderr output for debugging
    while not stderr_queue.empty():
        err_line = stderr_queue.get_nowait()
        print("STDERR:", err_line.strip(), file=sys.stderr)

    proc.terminate()
    proc.wait(timeout=5)

if __name__ == "__main__":
    main()
