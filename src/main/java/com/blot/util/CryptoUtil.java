package com.blot.util;

import com.loopj.android.http.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

public class CryptoUtil {

	public static Cipher cipherRSA = null;

	private CryptoUtil() throws NoSuchAlgorithmException, NoSuchPaddingException {
		// No constructor required
		cipherRSA = Cipher.getInstance("RSA");
	}

	public static final Logger LOGGER = LoggerFactory.getLogger(CryptoUtil.class);
	private static final String AES_MODE = "AES/CBC/PKCS5Padding";
	private static final String HASH_ALGORITHM = "SHA-256";
	public static final boolean DEBUG_LOG_ENABLED = true;
	public static final String IV = "ventesavenues123";
	private static final String TAG = "Crypto";

	public static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
		}
		return data;
	}

	protected static final char[] hexArray = "0123456789ABCDEF".toCharArray();

	public static String bytesToHex(byte[] bytes) {
		char[] hexChars = new char[bytes.length * 2];
		for (int j = 0; j < bytes.length; j++) {
			int v = bytes[j] & 0xFF;
			hexChars[j * 2] = hexArray[v >>> 4];
			hexChars[j * 2 + 1] = hexArray[v & 0x0F];
		}
		return new String(hexChars);
	}

	static byte[] encryptStringToBytes(String plainText, byte[] key, byte[] iv) {
		if (plainText == null || plainText.length() <= 0) {
			if (DEBUG_LOG_ENABLED) {
				LOGGER.error("plain text empty");
			}
			return new byte[0];
		}
		if (key == null || key.length <= 0 && DEBUG_LOG_ENABLED) {
			LOGGER.error("key is empty");
		}
		if (iv == null || iv.length <= 0 && DEBUG_LOG_ENABLED) {
			LOGGER.error("IV key empty");
		}
		byte[] encrypted = null;

		try {
			Cipher cipher = Cipher.getInstance(AES_MODE);
			SecretKeySpec myKey = new SecretKeySpec(key, "AES");
			IvParameterSpec iVKey = new IvParameterSpec(iv);
			cipher.init(Cipher.ENCRYPT_MODE, myKey, iVKey);

			encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
		} catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException
				| InvalidAlgorithmParameterException | BadPaddingException | IllegalBlockSizeException e) {
			LOGGER.error("context", e);
		}
		return encrypted;
	}

	static byte[] decryptStringToBytes(String plainText, byte[] key, byte[] iv) {
		if (plainText == null || plainText.length() <= 0) {
			if (DEBUG_LOG_ENABLED) {
				LOGGER.error("plain text empty");
			}
			return new byte[0];
		}
		if (key == null || key.length <= 0 && DEBUG_LOG_ENABLED) {
			LOGGER.error("key is empty");
		}
		if (iv == null || iv.length <= 0 && DEBUG_LOG_ENABLED) {
			LOGGER.error("IV key empty");
		}
		byte[] decrypted;

		try {
			Cipher cipher = Cipher.getInstance(AES_MODE);
			SecretKeySpec myKey = new SecretKeySpec(key, "AES");
			IvParameterSpec iVKey = new IvParameterSpec(iv);
			cipher.init(Cipher.DECRYPT_MODE, myKey, iVKey);

			decrypted = cipher.doFinal(Base64.decode(plainText.getBytes(StandardCharsets.UTF_8), Base64.NO_WRAP));
			if (LOGGER.isDebugEnabled()) {
				LOGGER.info(TAG, Arrays.toString(decrypted));
			}
			return decrypted;
		} catch (InvalidKeyException | NoSuchAlgorithmException | NoSuchPaddingException
				| InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {
			LOGGER.info(TAG + "saa {}", e);
		}
		return new byte[0];
	}

	public static String encrypt(String input, String passphrase, byte[] iv) {
		if (input.equalsIgnoreCase("") || passphrase.equalsIgnoreCase(""))
			return "";
		else {

			byte[] passphrasedata = passphrase.getBytes(StandardCharsets.UTF_8);
			MessageDigest md = null;
			try {
				md = MessageDigest.getInstance(HASH_ALGORITHM);
			} catch (NoSuchAlgorithmException e) {
				LOGGER.info(TAG + "saa {}", e);
			}
			if (md != null) {
				byte[] currentHash = md.digest(passphrasedata);
				if (LOGGER.isDebugEnabled()) {
					LOGGER.info(TAG, "hash : {}", Base64.encodeToString(currentHash, Base64.NO_WRAP));
				}

				return Base64.encodeToString(encryptStringToBytes(input, currentHash, iv), Base64.NO_WRAP);
			}
			return "";
		}
	}

	public static String decrypt(String input, String passphrase, byte[] iv) {
		if (input.equalsIgnoreCase("") || passphrase.equalsIgnoreCase(""))
			return "";
		else {

			byte[] passphrasedata = passphrase.getBytes(StandardCharsets.UTF_8);
			MessageDigest md = null;
			try {
				md = MessageDigest.getInstance(HASH_ALGORITHM);
			} catch (NoSuchAlgorithmException e) {
				LOGGER.info(TAG, e);
			}
			if (md != null) {
				byte[] currentHash = md.digest(passphrasedata);
				if (LOGGER.isDebugEnabled()) {
					LOGGER.info(TAG, input);
					LOGGER.info(TAG, Base64.encodeToString(currentHash, Base64.NO_WRAP));
				}

				byte[] decryptedBytes = decryptStringToBytes(input, currentHash, iv);
				LOGGER.info(" decryptedBytes ", decryptedBytes);
				return new String(decryptedBytes, StandardCharsets.UTF_8);
			}
			return "";
		}
	}

	public static String encrypts(String message, String key) {
		return encrypt(message, key, hexStringToByteArray(bytesToHex(IV.getBytes(StandardCharsets.UTF_8))));
	}

	public static String decrypts(String message, String key) {
		return decrypt(message, key, hexStringToByteArray(bytesToHex(IV.getBytes(StandardCharsets.UTF_8))));
	}

}
