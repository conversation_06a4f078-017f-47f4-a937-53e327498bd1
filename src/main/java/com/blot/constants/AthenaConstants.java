package com.blot.constants;

public class AthenaConstants {

    public final static String ATHENA_QUERY_EXECUTION_ID = "QueryExecutionId";
    public final static String ATHENA_QUERY = "Query";
    public final static String ATHENA_RESULT_CONFIGURATION_OUTPUT_LOCATION = "OutputLocation";
    public final static String ATHENA_STATUS_STATE = "State";
    public final static String ATHENA_QUERY_STATUS_SUCCEEDED = "SUCCEEDED";
    public final static String ATHENA_QUERY_STATUS_FAILED = "FAILED";
    public final static String ATHENA_QUERY_STATUS_CANCELLED = "CANCELLED";
    public final static String ATHENA_QUERY_STATUS_QUEUED = "QUEUED";
    public final static String ATHENA_QUERY_STATUS_RUNNING = "RUNNING";
}
