import subprocess
import json

# Path to your dummy MCP server script
MCP_SCRIPT_PATH = "./dummy-mcp-java-proxy.py"

# Start the MCP subprocess
process = subprocess.Popen(
    ["python3", MCP_SCRIPT_PATH],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# Messages to send to the MCP server
messages = [
    {
        "jsonrpc": "2.0",
        "id": 0,
        "method": "initialize",
        "params": {
            "protocolVersion": "2025-06-18",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "0.0.1"
            }
        }
    },
    {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "calls": [
                {
                    "tool": "get_application",
                    "toolCallId": "call-1",
                    "input": {}
                }
            ]
        }
    }
]

# Send messages to MCP server
for message in messages:
    json_str = json.dumps(message)
    print(f"\nSENDING: {json_str}")
    process.stdin.write(json_str + "\n")
    process.stdin.flush()

# Read the responses
try:
    while True:
        line = process.stdout.readline()
        if line == '':
            break
        print(f"RECEIVED: {line.strip()}")
except Exception as e:
    print(f"❌ Error reading from server: {e}")

# Print any stderr output
stderr_output = process.stderr.read()
if stderr_output:
    print("\nSTDERR:\n" + stderr_output)
