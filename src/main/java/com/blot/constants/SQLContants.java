package com.blot.constants;

import java.util.Arrays;
import java.util.List;

public interface SQLContants {

	// Reporting constants

	// available columns

	public final static String NAME_COLUMN_NAME = "name";
	public final static String IS_DELETED_COLUMN_NAME = "is_deleted";
	public final static String APPLICATION_ID_COLUMN_NAME = "application_id";
	public final static String REFERRER_BY_CATEGORY_COLUMN_NAME = "referrer_by_category";
	public final static String EVENT_DATE_COLUMN_NAME = "event_date";
	public final static String REFERRER_BY_MEDIUM_COLUMN_NAME = "referrer_by_medium";
	public final static String SESSION_ID_COLUMN_NAME = "session_id";
	public final static String EPOCH_UTC_COLUMN_NAME = "epoch_utc";
	public final static String EVENT_NAME_COLUMN_NAME = "event_name";
	public final static String USER_ID_COLUMN = "user_id";
	public final static String APP_ID_COLUMN_NAME = "app_id";
	public final static String ORDER_ID_COLUMN_NAME = "data_order_id";
	public final static String ORDER_VALUE_COLUMN_NAME = "data_value";

	// derived columns
	public final static String TOTAL_REVENUE_COLUMN_NAME = "total_revenue";
	public final static String GRAND_TOTAL_COLUMN_NAME = "grand_total";
	public final static String ORDER_COUNT_COLUMN_NAME = "order_count";
	public final static String REVENUE_PERCENTAGE_COLUMN_NAME = "revenue_percentage";

	// tables and views
	public final static String CORE_EVENTS_TABLE_NAME = "core_events";
	public final static String APPLICATION_TABLE = "application";

	// column values
	public final static String EVENT_NAME_PURCHASE = "Purchase";
}
