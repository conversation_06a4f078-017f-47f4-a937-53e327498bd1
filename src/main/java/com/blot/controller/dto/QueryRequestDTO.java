package com.blot.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record QueryRequestDTO(@JsonProperty("start_date") String startDate,
                              @JsonProperty("end_date") String endDate,
                              @JsonProperty("attribution_type") String attributionType,
                              @JsonProperty("eventList") List<String> eventList,
                              @JsonProperty("app_id") String appId) {
}
