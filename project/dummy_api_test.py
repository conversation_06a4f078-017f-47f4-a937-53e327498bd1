"""
Dummy MCP Server for local API testing.
"""

from typing import Any
import httpx
from mcp.server.fastmcp import FastMC<PERSON>


# Constants
NWS_API_BASE = "http://localhost:8080"
TOKEN = "+jawgicebv2pfrqrutltw0kw1mi2tqm7ufesmzrpsdeooobwclet5zxhftqo1d62"

# ✅ Define the MCP server at module level
mcp = FastMCP(name="dummy_api")

async def make_nws_request(url: str) -> dict[str, Any] | None:
    """Make a request to the NWS API with proper error handling."""
    headers = {
        "User-Agent": "MCP-Inspector",
        "Accept": "application/json",
        "Token": TOKEN,
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f" Request failed: {e}")
            return None

@mcp.tool()
async def get_application() -> str:
    """Fetch list of applications from the local API."""
    points_url = f"{NWS_API_BASE}/api/v1/application"
    points_data = await make_nws_request(points_url)

    if not points_data or "applications" not in points_data:
        return " Unable to fetch applications."

    apps = points_data["applications"]
    if not isinstance(apps, list):
        return "⚠️ Unexpected response format."

    return "\n---\n".join(str(app) for app in apps)

#  Ensure CLI compatibility by exposing `mcp` at module level
# No need for `if __name__ == "__main__"` block when using `mcp dev`
