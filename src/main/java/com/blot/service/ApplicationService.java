package com.blot.service;

import com.blot.controller.dto.Application;
import com.blot.controller.dto.ApplicationListResponse;
import com.blot.controller.dto.UserData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

@Service
public class ApplicationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationService.class);

    private final RestTemplate restTemplate;
    private final AuthService authService;

    @Autowired
    private com.blot.security.TokenValidator tokenValidator;

    @Value("${external.api.url}")
    private String externalApiUrl;

    public ApplicationService(RestTemplate restTemplate, AuthService authService) {
        this.restTemplate = restTemplate;
        this.authService = authService;
    }

    @Tool(name = "get_applications", description = "Get a list of applications from Blotout. Requires a valid token.")
    public List<Application> getApplications(String token) {
        try {
            tokenValidator.validate(token);
            UserData userInfo = authService.getUserInfoFromRedis(token);
            LOGGER.debug("Validated token belongs to user: {}", userInfo.getUserId());
            String url = externalApiUrl + "/api/v1/application";
            HttpHeaders headers = new HttpHeaders();
            headers.set("token", token);
            HttpEntity<Void> request = new HttpEntity<>(headers);
            ResponseEntity<ApplicationListResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    request,
                    ApplicationListResponse.class
            );
            return response.getBody() != null ? response.getBody().getApplications() : Collections.emptyList();
        } catch (Exception e) {
            LOGGER.error("Failed to fetch applications", e);
            return Collections.emptyList();
        }
    }
}
