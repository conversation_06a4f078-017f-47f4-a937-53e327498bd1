"""
Dummy MCP Server for local API testing.
"""

from typing import Any
import httpx
from mcp.server.fastmcp.server import FastMCP
import sys, os
import traceback
 # ✅ Use correct import path

# Constants
NWS_API_BASE = "http://localhost:8080"
# TOKEN = "+jawgicebv2pfrqrutltw0kw1mi2tqm7ufesmzrpsdeooobwclet5zxhftqo1d62"
TOKEN = os.getenv("API_TOKEN", "+jawgicebv2pfrqrutltw0kw1mi2tqm7ufesmzrpsdeooobwclet5zxhftqo1d62")

# ✅ Define the MCP server at module level
mcp = FastMCP(name="java-proxy")

async def make_nws_request(url: str) -> dict[str, Any] | None:
    """Make a request to the NWS API with proper error handling."""
    headers = {
        "User-Agent": "MCP-Inspector",
        "Accept": "application/json",
        "Token": TOKEN,
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            print(response)
            return json.loads(response.text)
        except Exception:
            print(f"❌ MCP server crashed: {e}")
            traceback.print_exc(file=sys.stderr)
            return None

@mcp.tool()
async def get_application() -> str:
    """Fetch list of applications from the local API."""
    points_data = f"{NWS_API_BASE}/api/v1/application"
    apps = points_data["applications"]
    if not isinstance(apps, list):
        return f"⚠️ Unexpected format: {type(apps)}"

    if not isinstance(apps, list):
        return "⚠️ Unexpected format for applications."

    # summaries = []
    # for app in apps:
    #     providers = app.get("providers", [])
    #     provider_list = ", ".join(p["id"] for p in providers) if providers else "None"
    #
    #     summary = (
    #         f"🆔 **{app.get('application_id')}**\n"
    #         f"📛 Name: {app.get('name')}\n"
    #         f"💻 Platform: {app.get('platform')}\n"
    #         f"📦 Bundle ID: {app.get('bundle_id')}\n"
    #         f"🔑 API Key: {app.get('api_key_prod')}\n"
    #         f"🏷️ Type: {app.get('type')}\n"
    #         f"📅 Created: {app.get('created_date')}\n"
    #         f"🌐 Edge Tag URL: {app.get('edgeTagUrl')}\n"
    #         f"🔌 Providers: {provider_list}\n"
    #     )
    #     summaries.append(summary)

    return "Success"

if __name__ == "__main__":
    # Initialize and run the server
    try:
        mcp.run(transport='stdio')
    except Exception as e:
        print(f"❌ MCP server crashed: {e}")
        traceback.print_exc(file=sys.stderr)
