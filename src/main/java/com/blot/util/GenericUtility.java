package com.blot.util;

import com.blot.constants.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Configuration
public class GenericUtility {

	private static final Logger LOGGER = LoggerFactory.getLogger(GenericUtility.class);

	public static String getMD5Hash(String input) throws NoSuchAlgorithmException {
		MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] messageDigest = md.digest(input.getBytes());
		BigInteger no = new BigInteger(1, messageDigest);
		String hashtext = no.toString(16);
		while (hashtext.length() < 32) {
			hashtext = "0" + hashtext;
		}
		return hashtext;
	}

	public static List<String> getDateBetweenTwoDate(String startDateStr, String endDateStr, String diffType) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			LocalDate startDate = LocalDate.parse(startDateStr, formatter);
			LocalDate endDate = LocalDate.parse(endDateStr, formatter);
			List<String> listDate = new ArrayList<String>();
			while (startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
				listDate.add(startDate.toString());
				switch (diffType) {
				case Constant.GRANULARITY_DAY:
					startDate = startDate.plusDays(1);
					break;
				case Constant.GRANULARITY_WEEK:
					startDate = startDate.plusWeeks(1);

					break;
				case Constant.GRANULARITY_MONTH:
					startDate = startDate.plusMonths(1);
					break;
				default:
					break;
				}
			}
			return listDate;
		} catch (Exception e) {
			LOGGER.error(" getDateBetweenTwoDate Error {} " + e);
		}
		return null;
	}
}
