package com.blot.util;

import com.blot.constants.RedisConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisCluster;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;

@Configuration
public class RedisClusterUtil {

	public static final Logger LOGGER = LoggerFactory.getLogger(RedisClusterUtil.class);

	@Autowired
	JedisCluster jedisCluster;

	@Value("${redis.query.ttl}")
	private long ttl;

	public void setRedisValue(String key, String field, String value) {
		if (value != null) {
			key = RedisConstants.REDIS_KEY_PREFIX + key;
			key = key.toLowerCase();
			field = field.toLowerCase();
			LOGGER.info("hset key:=> {}, field:=> {}, value:=> {}", key, field, value);
			jedisCluster.hset(key, field, value);

		}
	}

	public void sadd(String key, String... value) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("sadd key:=> {}, value:=> {}", key, value);
		jedisCluster.sadd(key, value);

	}

	public void srem(String key, String... value) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("srem key:=> {}, value:=> {}", key, value);
		jedisCluster.srem(key, value);
	}

	public void hdel(String key, String fields) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		fields = fields.toLowerCase();
		LOGGER.info("hdel key:=> {}, fields:=> {}", key, fields);
		jedisCluster.hdel(key, fields);
	}

	public void hset(String key, String field, Object value) {
		if (value != null) {
			key = RedisConstants.REDIS_KEY_PREFIX + key;
			key = key.toLowerCase();
			field = field.toLowerCase();
			LOGGER.info("hset key:=> {}, field:=> {}, value:=> {}", key, field, value);
			jedisCluster.hset(key, field, value.toString());
		}
	}

	public void setValue(String key, String value) {
		if (value != null) {
			key = RedisConstants.REDIS_KEY_PREFIX + key;
			key = key.toLowerCase();
			LOGGER.info("set key:=> {}, value:=> {}", key, value);
			jedisCluster.set(key, value);
		}
	}

	public void setValueWithTtl(String key, String value, long ttlInSecond) {
		if (value != null) {
			key = RedisConstants.REDIS_KEY_PREFIX + key;
			key = key.toLowerCase();
			LOGGER.info("set key:=> {}, value:=> {}, expiry {}", key, value, ttlInSecond);
			jedisCluster.set(key.toLowerCase(), value);
			jedisCluster.expire(key.toLowerCase(), (int) ttlInSecond);
		}
	}

	public String getValue(String key) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("get key:=> {}", key);
		return jedisCluster.get(key);
	}

	public String hgetValue(String key, String field) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("get key:=> {}, value:=> {}", key, field);
		return jedisCluster.hget(key, field);
	}

	public long getTTL(String key) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("get key:=> {}", key);
		return jedisCluster.ttl(key);
	}

	public void deleteKey(String key) {
		key = RedisConstants.REDIS_KEY_PREFIX + key;
		key = key.toLowerCase();
		LOGGER.info("del key:=> {}", key);
		jedisCluster.del(key);
	}

	public void addedQueryReponse(String keyString, String byteString) throws IOException, NoSuchAlgorithmException {
		String redisKey = GenericUtility.getMD5Hash(keyString);
		setValueWithTtl(redisKey, byteString, ttl);
	}

	public String getQueryReponse(String keyString) throws IOException, NoSuchAlgorithmException {
		String redisKey = GenericUtility.getMD5Hash(keyString);
		return getValue(redisKey);
	}

}
