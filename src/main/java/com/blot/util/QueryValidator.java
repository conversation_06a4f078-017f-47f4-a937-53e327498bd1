package com.blot.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.regex.Pattern;

public class QueryValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(QueryValidator.class);

    private static final List<Pattern> DANGEROUS_PATTERNS = List.of(
            Pattern.compile("\\b(drop|delete|truncate|alter|create|insert|update)\\b", Pattern.CASE_INSENSITIVE),
            Pattern.compile(";\\s*(drop|delete|truncate|alter|create|insert|update)\\s+", Pattern.CASE_INSENSITIVE),
            Pattern.compile("--\\s*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("/\\*.*?\\*/", Pattern.DOTALL),
            Pattern.compile("xp_cmdshell", Pattern.CASE_INSENSITIVE),
            Pattern.compile("sp_executesql", Pattern.CASE_INSENSITIVE),
            Pattern.compile("exec\\s*\\(", Pattern.CASE_INSENSITIVE),
            Pattern.compile("\\bunion\\b\\s+.*\\bselect\\b", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
            Pattern.compile("information_schema", Pattern.CASE_INSENSITIVE),
            Pattern.compile("sys\\.", Pattern.CASE_INSENSITIVE)
    );

    /**
     * Validates a SQL query for potentially dangerous patterns or excessive size.
     * @param query SQL query string
     * @throws IllegalArgumentException if query is invalid
     */
    public static void validateQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            throw new IllegalArgumentException("Query cannot be empty");
        }
        String queryLower = query.toLowerCase();
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(queryLower).find()) {
                LOGGER.warn("Potentially dangerous SQL pattern detected: {}", pattern.pattern());
                throw new IllegalArgumentException("Query contains potentially dangerous pattern: " + pattern.pattern());
            }
        }
        if (query.length() > 100_000) {
            throw new IllegalArgumentException("Query is too large (max 100KB)");
        }
        LOGGER.debug("Query validation passed. Length: {}", query.length());
    }

    /**
     * Sanitizes a database or schema identifier.
     * @param identifier Raw input string
     * @return Sanitized identifier
     * @throws IllegalArgumentException if identifier is invalid
     */
    public static String sanitizeIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new IllegalArgumentException("Schema identifier cannot be empty");
        }
        String sanitized = identifier.trim().replaceAll("[^a-zA-Z0-9_-]", "");
        if (sanitized.isEmpty()) {
            throw new IllegalArgumentException("Schema identifier contains only invalid characters");
        }
        if (sanitized.length() > 255) {
            throw new IllegalArgumentException("Schema identifier is too long (max 255 characters)");
        }
        LOGGER.debug("Sanitized schema: {} -> {}", identifier, sanitized);
        return sanitized;
    }
}
