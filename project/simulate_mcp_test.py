import sys
import json
import time
import subprocess

def send(message):
    print(json.dumps(message), flush=True)

# Step 1: Send initialize
send({
    "jsonrpc": "2.0",
    "id": 0,
    "method": "initialize",
    "params": {
        "protocolVersion": "2025-06-18",
        "capabilities": {},
        "clientInfo": {"name": "test", "version": "0.0.1"}
    }
})

# Step 2: Send tool call (include toolCallId in both input and call)
send({
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "calls": [
            {
                "tool": "get_application",
                "toolCallId": "call-1",
                "input": {}
            }
        ]
    }
})

# Step 3: Read back responses from the MCP server
# This assumes you're piping this script into the MCP server's stdin
# So run `simulate_mcp_test.py` as a subprocess with `stdin=PIPE`, `stdout=PIPE` in a driver

# For quick test, let’s try reading a few lines
try:
    while True:
        line = sys.stdin.readline()
        if not line:
            break
        print("RECEIVED:", line.strip())
except KeyboardInterrupt:
    print("Stopped by user.")
