package com.blot.constants;

/**
 * The interface RedisConstants.
 */
public interface RedisConstants {

	public final static String TOKEN_KEY_PREFIX = "tkn:";
	public final static String L_PUB_T = "lpubt";
	public final static String APPLICATION_KEY_PREFIX = "app:";
	public final static String MANIFEST_KEY_PREFIX = "manifest:";
	public final static String DELIVER_STREAM_FIELD = "ds";
	public final static String APP_BUNDLE_ID = "bundle";
	public final static String APP_TYPE = "type";
	public final static String REDIS_KEY_PREFIX = "bo:";
	public final static String APP_NAME = "name";
	public final static String APP_ID = "id";
	public final static String PIPELINE_KEY_SUFFIX = "pipeline:flag";
	public final static String ACTIVATION_KEY_PREFIX = "activation:";
	public final static String CHANNEL_NAME_FIELD = "channel";
	public final static String CONNECTION_NAME_FIELD = "connection";
	public final static String SCERETS_FIELD = "scerets";
	public final static String SYNC_TYPE_FIELD = "sync";
	public final static String TAG_SEGMENTS_FILED = "tags";

}
