package com.blot.security;

import com.blot.service.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class TokenValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(TokenValidator.class);
    private final AuthService authService;

    public TokenValidator(AuthService authService) {
        this.authService = authService;
    }

    public void validate(String token) {
        if (!authService.isTokenValid(token)) {
            LOGGER.warn("Invalid or expired token received: {}...", token != null ? token.substring(0, Math.min(10, token.length())) : "null");
            throw new IllegalArgumentException("[AUTH_ERROR] Invalid or expired token.");
        }
    }
}