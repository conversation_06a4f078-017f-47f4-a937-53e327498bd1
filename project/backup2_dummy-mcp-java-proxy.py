#!/usr/bin/env python3
import os
import sys
import traceback
from typing import Any, Dict
from mcp.server.fastmcp.server import FastMCP
from pydantic import BaseModel
import httpx

# Define input model for the tool (empty because no input needed)
class GetApplicationInput(BaseModel):
    pass

# Load token from environment or use fallback
TOKEN = os.getenv("API_TOKEN", "+jawgicebv2pfrqrutltw0kw1mi2tqm7ufesmzrpsdeooobwclet5zxhftqo1d62")
BASE_URL = "http://localhost:8080"

# Initialize MCP server
mcp = FastMCP(name="java-proxy")

# Async helper function to fetch applications from local API
async def fetch_apps() -> Dict[str, Any] | None:
    headers = {
        "Accept": "application/json",
        "Token": TOKEN,
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/api/v1/application", headers=headers, timeout=10.0)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Request failure: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            return None

# MCP tool handler, requires input argument typed as GetApplicationInput
@mcp.tool()
async def get_application(input: GetApplicationInput) -> str:
    print("🚨 get_application tool called", file=sys.stderr)
    data = await fetch_apps()
    if data is None:
        return "❌ Failed to fetch application data."

    apps = data.get("applications", [])
    if not isinstance(apps, list):
        return "⚠️ Unexpected format for applications."

    return f"✅ Found {len(apps)} applications."

def main():
    print("🚀 Starting MCP server...", file=sys.stderr)
    try:
        mcp.run(transport="stdio")
    except Exception as e:
        print(f"❌ MCP server crashed: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)

if __name__ == "__main__":
    main()
