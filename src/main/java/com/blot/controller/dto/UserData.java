package com.blot.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserData implements Serializable {

    private static final long serialVersionUID = -2943528640173372683L;

    public UserData() {
        super();
    }

    public UserData(String userId) {
        super();
        this.userId = userId;

    }

    @JsonProperty("token")
    private String token;

    @JsonProperty("expiry")
    private long expiry;

    @JsonProperty("timestamp")
    private long timestamp;

    @JsonProperty("name")
    private String name;

    @JsonProperty("profile")
    private String profile;

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("account_id")
    private String accountId;

    @JsonProperty("account_type")
    private String accountType;

    @JsonProperty("bucket_name")
    private String bucketName;

    @JsonProperty("default_app")
    private String defaultApp;

    @JsonProperty("default_mode")
    private String defaultMode;

    @JsonProperty("permission")
    private List<String> permission;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getPermission() {
        return permission;
    }

    public void setPermission(List<String> permission) {
        this.permission = permission;
    }

    public long getExpiry() {
        return expiry;
    }

    public void setExpiry(long expiry) {
        this.expiry = expiry;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getDefaultApp() {
        return defaultApp;
    }

    public void setDefaultApp(String defaultApp) {
        this.defaultApp = defaultApp;
    }

    public String getDefaultMode() {
        return defaultMode;
    }

    public void setDefaultMode(String defaultMode) {
        this.defaultMode = defaultMode;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    @Override
    public String toString() {
        return "UserData [token=" + token + ", expiry=" + expiry + ", timestamp=" + timestamp
                + ", name=" + name + ", profile=" + profile + ", id=" + id + ", status=" + status + ", userId=" + userId
                + ", accountId=" + accountId + ", bucketName=" + bucketName + ", defaultApp=" + defaultApp
                + ", defaultMode=" + defaultMode + ", permission=" + permission + "]";
    }

}
