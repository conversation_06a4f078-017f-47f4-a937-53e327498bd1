package com.blot.service;

import com.blot.constants.RedisConstants;
import com.blot.controller.dto.UserData;
import com.blot.util.CryptoUtil;
import com.blot.util.JacksonUtil;
import com.blot.util.RedisClusterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class AuthService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthService.class);
    private static final String TOKEN_PREFIX = "tkn:";

    private final RestTemplate restTemplate;
    private final RedisClusterUtil redisUtil;

    @Value("${external.api.url}")
    private String externalApiUrl;

    @Value("${cryptoKey}")
    private String cryptoKey;

    @Value("${token.expiry}")
    private long tokenTtlInSeconds; // e.g. 3600

    public AuthService(RestTemplate restTemplate, RedisClusterUtil redisUtil) {
        this.restTemplate = restTemplate;
        this.redisUtil = redisUtil;
    }

    @Tool(name = "get_token", description = "Authenticate user with user_id and password. Returns and stores token.")
    public String getToken(String userId, String password) {
        try {
            String loginUrl = externalApiUrl + "/api/v1/login";
            Map<String, String> loginBody = Map.of(
                    "user_id", userId,
                    "password", password
            );
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> request = new HttpEntity<>(loginBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(loginUrl, request, Map.class);
            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                throw new RuntimeException("Login failed. No response or invalid status.");
            }
            String url = (String) response.getBody().get("url");
            if (url == null) {
                throw new RuntimeException("Login failed. No URL in response.");
            }
            String token = extractBoTokenFromUrl(url);
            if (token == null) {
                throw new RuntimeException("Failed to extract token from login response.");
            }
            return token;
        } catch (Exception e) {
            LOGGER.error("❌ Error during authentication for user: {}", userId, e);
            return "❌ Authentication failed: " + e.getMessage();
        }
    }

    @Tool(name = "logout", description = "Invalidate the current token and log out the user.")
    public String logout(String token) {
        String redisKey = TOKEN_PREFIX + token;
        if (redisUtil.getValue(redisKey) != null) {
            redisUtil.deleteKey(redisKey);
            return "✅ Logged out successfully. Token invalidated.";
        } else {
            return "❌ Token already invalid or expired.";
        }
    }

    /*@Tool(name = "get_token_info", description = "Decode and inspect JWT token to view expiry, user info, and claims.")
    public Map<String, Object> getTokenInfo(String token) {
        UserData userInfo = getUserInfoFromRedis(token);
        Map<String, Object> userInfoMap = new ConcurrentHashMap<>();
        if(Objects.nonNull(userInfoMap)) {
            userInfoMap.put("user_id", userInfo.getUserId());
            userInfoMap.put("name", userInfo.getName());
            userInfoMap.put("expiry ttl", userInfo.getExpiry());
            userInfoMap.put("permission", userInfo.getPermission());
        }
        return userInfoMap;
    }*/

    private String extractBoTokenFromUrl(String urlString) {
        try {
            URI uri = new URI(urlString);
            String query = uri.getRawQuery();
            Map<String, String> params = Arrays.stream(query.split("&"))
                    .map(s -> s.split("="))
                    .filter(arr -> arr.length == 2)
                    .collect(Collectors.toMap(
                            kv -> URLDecoder.decode(kv[0], StandardCharsets.UTF_8),
                            kv -> URLDecoder.decode(kv[1], StandardCharsets.UTF_8)
                    ));
            return params.get("bo");
        } catch (Exception e) {
            LOGGER.error("Failed to parse token from URL: {}", urlString, e);
            return null;
        }
    }

    public boolean isTokenValid(String token) {
        return redisUtil.getValue(TOKEN_PREFIX + token) != null;
    }

    public UserData getUserInfoFromRedis(String token) throws IllegalArgumentException {
        String authString = redisUtil.getValue(RedisConstants.TOKEN_KEY_PREFIX + token);
        if (authString == null) {
            throw new IllegalArgumentException("Invalid Token");
        }
        long expiry = redisUtil.getTTL(RedisConstants.TOKEN_KEY_PREFIX + token);
        String data = CryptoUtil.decrypts(authString, cryptoKey);
        UserData authResponse = JacksonUtil.fromString(data, UserData.class);
        if (authResponse == null) {
            throw new IllegalArgumentException("Invalid Token");
        }
        authResponse.setExpiry(expiry);
        return authResponse;
    }
}
