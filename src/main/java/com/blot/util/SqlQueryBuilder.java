package com.blot.util;

import com.blot.controller.dto.QueryRequestDTO;
import static com.blot.constants.SQLContants.*;

import com.blot.enums.QueryType;
import org.springframework.context.annotation.Configuration;
import java.util.*;

@Configuration
public class SqlQueryBuilder {

    private static final String FIRST_CLICK_ATTRIBUTION_QUERY = "WITH conversion_sessions AS (" +
            " SELECT {SESSION_ID_COLUMN_NAME}, {ORDER_ID_COLUMN_NAME}," +
            " CASE " +
            " WHEN {ORDER_VALUE_COLUMN_NAME} IS NULL OR {ORDER_VALUE_COLUMN_NAME} = '' OR {ORDER_VALUE_COLUMN_NAME} = 'undefined' THEN 0.0" +
            " ELSE TRY_CAST({ORDER_VALUE_COLUMN_NAME} AS DOUBLE)" +
            " END AS {ORDER_VALUE_COLUMN_NAME}" +
            " FROM {CORE_EVENTS_TABLE_NAME} WHERE " +
            " {EVENT_DATE_COLUMN_NAME} >= '{EVENT_DATE_FROM_VALUE}'" +
            " AND {EVENT_DATE_COLUMN_NAME} <= '{EVENT_DATE_TO_VALUE}'" +
            " AND {EVENT_NAME_COLUMN_NAME} = '{EVENT_NAME_PURCHASE}'" +
            " AND {APP_ID_COLUMN_NAME} = '{APP_ID_COLUMN_VALUE}'" +
            " )," +
            " first_click_utms AS (" +
            " SELECT e.{REFERRER_BY_CATEGORY_COLUMN_NAME}, e.{REFERRER_BY_MEDIUM_COLUMN_NAME}," +
            " c.{ORDER_ID_COLUMN_NAME}, c.{ORDER_VALUE_COLUMN_NAME}," +
            " ROW_NUMBER() OVER(PARTITION BY e.{SESSION_ID_COLUMN_NAME} ORDER BY e.{EPOCH_UTC_COLUMN_NAME}) AS rn" +
            " FROM {CORE_EVENTS_TABLE_NAME} e, conversion_sessions c WHERE" +
            " {EVENT_DATE_COLUMN_NAME} >= '{EVENT_DATE_FROM_VALUE}'" +
            " AND e.{EVENT_DATE_COLUMN_NAME} <= '{EVENT_DATE_TO_VALUE}'" +
            " AND e.{APP_ID_COLUMN_NAME} = '{APP_ID_COLUMN_VALUE}'" +
            " AND e.{SESSION_ID_COLUMN_NAME} = c.{SESSION_ID_COLUMN_NAME}" +
            " )," +
            " first_click_stats AS (" +
            " SELECT {REFERRER_BY_CATEGORY_COLUMN_NAME}, " +
            " COALESCE(NULLIF({REFERRER_BY_MEDIUM_COLUMN_NAME},''), 'Direct Traffic') AS {REFERRER_BY_MEDIUM_COLUMN_NAME}," +
            " COUNT(DISTINCT {ORDER_ID_COLUMN_NAME}) AS {ORDER_COUNT_COLUMN_NAME}," +
            " ROUND(SUM(CAST({ORDER_VALUE_COLUMN_NAME} AS DOUBLE)), 2) AS {TOTAL_REVENUE_COLUMN_NAME}" +
            " FROM first_click_utms GROUP BY 1, 2" +
            " )," +
            " total_revenue AS (" +
            " SELECT SUM({TOTAL_REVENUE_COLUMN_NAME}) AS {GRAND_TOTAL_COLUMN_NAME} FROM first_click_stats" +
            " )" +
            " SELECT f.{REFERRER_BY_CATEGORY_COLUMN_NAME}, f.{REFERRER_BY_MEDIUM_COLUMN_NAME}, f.{ORDER_COUNT_COLUMN_NAME}, f.{TOTAL_REVENUE_COLUMN_NAME}," +
            " ROUND((f.{TOTAL_REVENUE_COLUMN_NAME} / t.{GRAND_TOTAL_COLUMN_NAME}) * 100, 2) AS {REVENUE_PERCENTAGE_COLUMN_NAME}" +
            " FROM first_click_stats f, total_revenue t ORDER BY f.{TOTAL_REVENUE_COLUMN_NAME} DESC";

    private static final String LAST_CLICK_ATTRIBUTION_QUERY = "WITH conversion_events AS (" +
            " SELECT {REFERRER_BY_CATEGORY_COLUMN_NAME}, {REFERRER_BY_MEDIUM_COLUMN_NAME}, {USER_ID_COLUMN_NAME}, {SESSION_ID_COLUMN_NAME}," +
            " {ORDER_ID_COLUMN_NAME}, " +
            " CASE " +
            " WHEN {ORDER_VALUE_COLUMN_NAME} IS NULL OR {ORDER_VALUE_COLUMN_NAME} = '' OR {ORDER_VALUE_COLUMN_NAME} = 'undefined' THEN 0.0" +
            " ELSE TRY_CAST({ORDER_VALUE_COLUMN_NAME} AS DOUBLE)" +
            " END AS {ORDER_VALUE_COLUMN_NAME}" +
            " FROM {CORE_EVENTS_TABLE_NAME} WHERE {APP_ID_COLUMN_NAME} = '{APP_ID_COLUMN_VALUE}'" +
            " AND {EVENT_DATE_COLUMN_NAME} >= '{EVENT_DATE_FROM_VALUE}'" +
            " AND {EVENT_DATE_COLUMN_NAME} <= '{EVENT_DATE_TO_VALUE}'" +
            " AND {EVENT_NAME_COLUMN_NAME} = '{EVENT_NAME_PURCHASE}'" +
            " )," +
            " last_click_stats AS (" +
            " SELECT {REFERRER_BY_CATEGORY_COLUMN_NAME}, " +
            " COALESCE(NULLIF({REFERRER_BY_MEDIUM_COLUMN_NAME},''), 'Direct Traffic') AS {REFERRER_BY_MEDIUM_COLUMN_NAME}," +
            " COUNT(DISTINCT {ORDER_ID_COLUMN_NAME}) AS {ORDER_COUNT_COLUMN_NAME}," +
            " ROUND(SUM(CAST({ORDER_VALUE_COLUMN_NAME} AS DOUBLE)), 2) AS {TOTAL_REVENUE_COLUMN_NAME}" +
            " FROM conversion_events GROUP BY 1, 2" +
            " )," +
            " total_revenue AS (" +
            " SELECT SUM({TOTAL_REVENUE_COLUMN_NAME}) AS {GRAND_TOTAL_COLUMN_NAME} FROM last_click_stats" +
            " )" +
            " SELECT l.{REFERRER_BY_CATEGORY_COLUMN_NAME}, l.{REFERRER_BY_MEDIUM_COLUMN_NAME}, l.{ORDER_COUNT_COLUMN_NAME}, l.{TOTAL_REVENUE_COLUMN_NAME}," +
            " ROUND((l.{TOTAL_REVENUE_COLUMN_NAME} / t.{GRAND_TOTAL_COLUMN_NAME}) * 100, 2) AS {REVENUE_PERCENTAGE_COLUMN_NAME}" +
            " FROM last_click_stats l, total_revenue t ORDER BY l.{TOTAL_REVENUE_COLUMN_NAME} DESC";

    private static final String GET_ACTIVE_APP_ID_FOR_APP_NAME = "SELECT {APPLICATION_ID_COLUMN_NAME} FROM {APPLICATION_TABLE} " +
            " WHERE {IS_DELETED_COLUMN_NAME} = 0 AND {NAME_COLUMN_NAME} like '%{APP_NAME_PLACEHOLDER}%'";

    public static String generateQuery(QueryType type, QueryRequestDTO queryRequestDTO, String appName) throws Exception {
        if (type == QueryType.ATTRIBUTION) {
            return generateAttributionQuery(queryRequestDTO);
        } else if (type == QueryType.FUNNEL) {
            return generateFunnelQuery(queryRequestDTO);
        } else if (type == QueryType.APP_ID) {
            return generateActiveAppIdQuery(appName);
        }
        throw new IllegalArgumentException("Unsupported query type.");
    }


    public static String generateActiveAppIdQuery(String appName) {
        if (appName == null || appName.isEmpty()) {
            throw new IllegalArgumentException("App name cannot be null or empty.");
        }
        String query = GET_ACTIVE_APP_ID_FOR_APP_NAME.replace("{APP_NAME_PLACEHOLDER}", appName);
        return replaceQueryPlaceHolders(query, null);
    }

    public static String generateAttributionQuery(QueryRequestDTO queryRequest) {
        if (queryRequest == null || queryRequest.attributionType() == null) {
            throw new IllegalArgumentException("Invalid QueryRequestDTO for attribution query.");
        }
        String template = switch (queryRequest.attributionType()) {
            case "first", "first-click", "first_click" -> FIRST_CLICK_ATTRIBUTION_QUERY;
            case "last", "last-click", "last_click" -> LAST_CLICK_ATTRIBUTION_QUERY;
            default ->
                    throw new IllegalArgumentException("Unsupported attribution type: " + queryRequest.attributionType());
        };
        return replaceQueryPlaceHolders(template, queryRequest);
    }

    public static String generateFunnelQuery(QueryRequestDTO queryRequestDTO) {
        List<String> eventList = queryRequestDTO.eventList();
        if (eventList == null || eventList.isEmpty()) {
            return "Please provide at least one event to generate a funnel query.";
        }

        StringBuilder queryBuilder = new StringBuilder();
        List<String> cteList = new ArrayList<>();
        List<String> selectList = new ArrayList<>();
        String previousAlias = null;

        for (int i = 0; i < eventList.size(); i++) {
            String event = eventList.get(i).trim();
            String alias = event.toLowerCase().replaceAll("[^a-zA-Z0-9]", "_") + "_users";

            // CTE block
            StringBuilder cte = new StringBuilder();
            cte.append(alias).append(" AS (\n")
                    .append("    SELECT c.{USER_ID_COLUMN_NAME}, MIN({EPOCH_UTC_COLUMN_NAME}) AS min_time FROM {CORE_EVENTS_TABLE_NAME} c");

            if (i > 0) {
                cte.append(", ").append(previousAlias).append(" p");
            }

            cte.append("\n    WHERE c.{EVENT_DATE_COLUMN_NAME} >= '").append(queryRequestDTO.startDate()).append("'")
                    .append(" AND c.{EVENT_DATE_COLUMN_NAME} <= '").append(queryRequestDTO.endDate()).append("'")
                    .append("\n    AND c.{APP_ID_COLUMN_NAME} = '").append(queryRequestDTO.appId()).append("'")
                    .append("\n    AND c.{EVENT_NAME_COLUMN_NAME} = '").append(event).append("'");

            if (i > 0) {
                cte.append("\n    AND c.{USER_ID_COLUMN_NAME} = p.{USER_ID_COLUMN_NAME}")
                        .append("\n    AND c.{EPOCH_UTC_COLUMN_NAME} >= p.min_time");
            }

            cte.append("\n    GROUP BY c.{USER_ID_COLUMN_NAME}\n)");
            cteList.add(cte.toString());
            previousAlias = alias;

            // Final SELECT block
            selectList.add(
                    "SELECT '" + event + "' AS step_name, COUNT(*) AS total_users, " + i + " AS step_order FROM " + alias
            );
        }

        // Combine all parts
        queryBuilder.append("WITH ");
        queryBuilder.append(String.join(",\n", cteList));
        queryBuilder.append("\n\n");
        queryBuilder.append(String.join("\nUNION ALL\n", selectList));
        queryBuilder.append("\nORDER BY step_order;");

        return replaceQueryPlaceHolders(queryBuilder.toString(), queryRequestDTO);
    }

    private static String replaceQueryPlaceHolders(String query, QueryRequestDTO queryRequest) {
        if(Objects.nonNull(query)) {
            return query
                    .replace("{REFERRER_BY_CATEGORY_COLUMN_NAME}", REFERRER_BY_CATEGORY_COLUMN_NAME)
                    .replace("{REFERRER_BY_MEDIUM_COLUMN_NAME}", REFERRER_BY_MEDIUM_COLUMN_NAME)
                    .replace("{USER_ID_COLUMN_NAME}", USER_ID_COLUMN)
                    .replace("{SESSION_ID_COLUMN_NAME}", SESSION_ID_COLUMN_NAME)
                    .replace("{ORDER_ID_COLUMN_NAME}", ORDER_ID_COLUMN_NAME)
                    .replace("{ORDER_VALUE_COLUMN_NAME}", ORDER_VALUE_COLUMN_NAME)
                    .replace("{CORE_EVENTS_TABLE_NAME}", CORE_EVENTS_TABLE_NAME)
                    .replace("{APP_ID_COLUMN_NAME}", APP_ID_COLUMN_NAME)
                    .replace("{APP_ID_COLUMN_VALUE}", queryRequest!=null && queryRequest.appId()!=null ? queryRequest.appId() : "")
                    .replace("{EVENT_DATE_COLUMN_NAME}", EVENT_DATE_COLUMN_NAME)
                    .replace("{EVENT_DATE_FROM_VALUE}", queryRequest!=null && queryRequest.startDate()!=null ? queryRequest.startDate() : "")
                    .replace("{EVENT_DATE_TO_VALUE}", queryRequest!=null && queryRequest.endDate() != null ? queryRequest.endDate() : "")
                    .replace("{EVENT_NAME_COLUMN_NAME}", EVENT_NAME_COLUMN_NAME)
                    .replace("{EVENT_NAME_PURCHASE}", EVENT_NAME_PURCHASE)
                    .replace("{TOTAL_REVENUE_COLUMN_NAME}", TOTAL_REVENUE_COLUMN_NAME)
                    .replace("{GRAND_TOTAL_COLUMN_NAME}", GRAND_TOTAL_COLUMN_NAME)
                    .replace("{ORDER_COUNT_COLUMN_NAME}", ORDER_COUNT_COLUMN_NAME)
                    .replace("{REVENUE_PERCENTAGE_COLUMN_NAME}", REVENUE_PERCENTAGE_COLUMN_NAME)
                    .replace("{EPOCH_UTC_COLUMN_NAME}", EPOCH_UTC_COLUMN_NAME)
                    .replace("{APPLICATION_ID_COLUMN_NAME}", APPLICATION_ID_COLUMN_NAME)
                    .replace("{APPLICATION_TABLE}", APPLICATION_TABLE)
                    .replace("{NAME_COLUMN_NAME}", NAME_COLUMN_NAME)
                    .replace("{IS_DELETED_COLUMN_NAME}", IS_DELETED_COLUMN_NAME);
        } else return null;
    }

    /*public static void main(String[] args) throws Exception {
        String appQuery = SqlQueryBuilder.generateQuery(QueryType.APP_ID, null, "abc");
        System.out.println(appQuery);
    }*/
}
