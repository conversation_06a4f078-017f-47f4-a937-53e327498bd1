name: Deploy

on:
  pull_request:
    branches: [ main ]

env:
  DOCKER_TAG: |
    ${{ github.event.pull_request.head.ref }}-${{ github.event.pull_request.head.sha }}

jobs:
  deploy:
    if: ${{ github.event_name != 'pull_request' || (github.event_name == 'pull_request' && !github.event.pull_request.draft) }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    - name: Build and push
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64
        push: true
        tags: blotout/mcp-service:${{ env.DOCKER_TAG }}

    - name: Comment
      uses: NejcZdovc/comment-pr@v1
      with:
        message: "Docker tag: `${{ env.DOCKER_TAG }}`"
      env:
        GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
