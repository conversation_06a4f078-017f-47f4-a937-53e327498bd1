package com.blot.setup;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.nio.file.*;
import java.util.Scanner;

public class ClaudeMcpSetup {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static void main(String[] args) {
        System.out.println("🚀 Blotout Athena MCP Setup for Claude Desktop");
        System.out.println("=".repeat(50));

        try {
            Path configPath = getClaudeConfigPath();
            System.out.println("📁 Claude config path: " + configPath);

            Scanner scanner = new Scanner(System.in);

            // Input values
            System.out.print("Enter absolute path to mcp-service JAR file: ");
            String jarPath = scanner.nextLine().trim();

            System.out.print("Enter ENV (e.g., dev/prod): ");
            String env = scanner.nextLine().trim();

            System.out.print("Enter Dashboard Backend API URL: ");
            String backendUrl = scanner.nextLine().trim();

            System.out.print("Enter Dashboard Backend API Token: ");
            String apiToken = scanner.nextLine().trim();

            System.out.print("Enter AWS Access Key ID: ");
            String awsAccessKeyId = scanner.nextLine().trim();

            System.out.print("Enter AWS Secret Access Key: ");
            String awsSecretAccessKey = scanner.nextLine().trim();

            System.out.print("Enter AWS Region [us-east-1]: ");
            String awsRegion = scanner.nextLine().trim();
            if (awsRegion.isEmpty()) awsRegion = "us-east-1";

            System.out.print("Enter Amazon Account ID: ");
            String accountId = scanner.nextLine().trim();

            System.out.print("Enter Athena Timeout (seconds) [60]: ");
            String athenaTimeout = scanner.nextLine().trim();
            if (athenaTimeout.isEmpty()) athenaTimeout = "60";

            System.out.print("Enter Athena Default Database: ");
            String athenaDb = scanner.nextLine().trim();

            System.out.print("Enter Athena Output Bucket (s3://...): ");
            String athenaBucket = scanner.nextLine().trim();

            System.out.print("Enter Athena Access Key ID: ");
            String athenaAccessKey = scanner.nextLine().trim();

            System.out.print("Enter Athena Secret Access Key: ");
            String athenaSecretKey = scanner.nextLine().trim();

            System.out.print("Enter Athena Region [us-east-1]: ");
            String athenaRegion = scanner.nextLine().trim();
            if (athenaRegion.isEmpty()) athenaRegion = "us-east-1";

            // Construct env
            ObjectNode envNode = mapper.createObjectNode();
            envNode.put("ENV", env);
            envNode.put("DASHBOARD_BACKEND_API_URL", backendUrl);
            envNode.put("DASHBOARD_BACKEND_API_TOKEN", apiToken);
            envNode.put("AWS_ACCESS_KEY_ID", awsAccessKeyId);
            envNode.put("AWS_SECRET_ACCESS_KEY", awsSecretAccessKey);
            envNode.put("AWS_DEFAULT_REGION", awsRegion);
            envNode.put("AMAZON_ACCOUNT_ID", accountId);
            envNode.put("ATHENA_TIMEOUT", athenaTimeout);
            envNode.put("ATHENA_DATABASE", athenaDb);
            envNode.put("ATHENA_OUTPUT_BUCKET", athenaBucket);
            envNode.put("ATHENA_ACCESS_KEY_ID", athenaAccessKey);
            envNode.put("ATHENA_SECRET_ACCES_KEY", athenaSecretKey);
            envNode.put("ATHENA_REGION", athenaRegion);

            // MCP config
            ObjectNode mcpConfig = mapper.createObjectNode();
            mcpConfig.put("command", "java");
            mcpConfig.putArray("args")
                    .add("-jar")
                    .add(jarPath);
            mcpConfig.set("env", envNode);

            ObjectNode mcpServers = mapper.createObjectNode();
            mcpServers.set("blotout-athena-api-mcp-service", mcpConfig);

            ObjectNode fullConfig = loadOrCreateConfig(configPath);
            fullConfig.set("mcpServers", mcpServers);

            // Write file
            Files.createDirectories(configPath.getParent());
            mapper.writerWithDefaultPrettyPrinter().writeValue(configPath.toFile(), fullConfig);
            System.out.println("✅ Configuration saved to " + configPath);

            System.out.println("\n📋 Final configuration:");
            System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(fullConfig));

            System.out.println("\n🎉 Setup complete!");
            System.out.println("1. Restart Claude Desktop");
            System.out.println("2. Use Claude with your custom MCP server");

        } catch (Exception e) {
            System.err.println("❌ Setup failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Path getClaudeConfigPath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("mac")) {
            return Paths.get(System.getProperty("user.home"), "Library", "Application Support", "Claude", "claude_desktop_config.json");
        } else if (os.contains("win")) {
            return Paths.get(System.getenv("APPDATA"), "Claude", "claude_desktop_config.json");
        } else {
            return Paths.get(System.getProperty("user.home"), ".config", "claude", "claude_desktop_config.json");
        }
    }

    private static ObjectNode loadOrCreateConfig(Path configPath) {
        try {
            if (Files.exists(configPath)) {
                return (ObjectNode) mapper.readTree(configPath.toFile());
            }
        } catch (IOException e) {
            System.out.println("⚠️ Could not read existing config, starting fresh.");
        }
        return mapper.createObjectNode();
    }
}
