# ---- Stage 1: Build the application ----
FROM maven:3.8.4-openjdk-17 AS builder

# Set working directory
WORKDIR /app

# Add explicit configuration for MCP server

#ENV SPRING_AI_MCP_SERVER_SSE_ENDPOINT=/mcp/sse \
    #SPRING_AI_MCP_SERVER_SSE_MESSAGE_ENDPOINT=/mcp/sse/message

# Copy source code
COPY . .

# Clean up unnecessary files (optional)
RUN rm -rf .git

# Build the JAR (skip tests if needed)
RUN mvn clean install -DskipTests

# ---- Stage 2: Run the application ----
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Copy built JAR from builder stage
COPY --from=builder /app/target/mcp-service-0.0.1-SNAPSHOT.jar /app/mcp-service.jar

# Expose default Spring Boot port
#EXPOSE 8080

# Run the application
ENTRYPOINT ["java", "-jar", "/app/mcp-service.jar"]