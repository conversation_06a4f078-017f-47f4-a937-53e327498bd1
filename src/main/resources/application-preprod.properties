spring.application.name=mcp-service
spring.ai.mcp.server.name=blotout-athena-api-mcp-service

spring.ai.mcp.server.stdio=false
spring.ai.mcp.server.enabled=true
spring.ai.mcp.server.type=SYNC
spring.ai.mcp.server.capabilities.tool=true
spring.ai.mcp.server.capabilities.resource=false
spring.ai.mcp.server.capabilities.prompt=false
spring.ai.mcp.server.capabilities.completion=false
spring.ai.mcp.server.base-url=/mcp
spring.ai.mcp.server.sse-endpoint=/sse
spring.ai.mcp.server.sse-message-endpoint=/sse/message

server.port=${SERVER_PORT:8080}
cryptoKey=${CRYPTOKEY:blotout}

logging.level.org.springframework.ai.mcp=DEBUG
logging.level.com.blot=DEBUG

external.api.url=${DASHBOARD_BACKEND_API_URL}
external.api.token=${DASHBOARD_BACKEND_API_TOKEN}

#Admin Keys
amazon.access-key=${AWS_ACCESS_KEY_ID}
amazon.secret-key=${AWS_SECRET_ACCESS_KEY}
amazon.region=${AWS_DEFAULT_REGION:us-east-1}
amazon.account.id=${AMAZON_ACCOUNT_ID}

#Athena's connections properties
datasource.athena.timeout=${ATHENA_TIMEOUT:100000}
datasource.athena.database=${ATHENA_DATABASE}
datasource.athena.output.bucket=${ATHENA_OUTPUT_BUCKET}
datasource.athena.access-key=${ATHENA_ACCESS_KEY_ID}
datasource.athena.secret-key=${ATHENA_SECRET_ACCES_KEY}
datasource.athena.region=${ATHENA_REGION:us-east-1}

#Redis config
redis.query.ttl=${REDIS_QUERY_TTL:600}
redis.cluster.nodes=${REDIS_CLUSTER_NODES}:6379
redis.cluster.minIdle = 10
redis.cluster.maxIdle = 100
redis.cluster.maxTotal = 1024
redis.cluster.testOnBorrow=  true
redis.cluster.testOnReturn= true
redis.cluster.testWhileIdle= true
redis.cluster.timeBetweenEvictionRunsMillis=  10000
redis.cluster.numTestsPerEvictionRun=  10
redis.cluster.blockWhenExhausted=  true
redis.cluster.maxWaitMillis=  10000
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=5

#Auth config
token.expiry=${TOKEN_EXPIRY:86400}