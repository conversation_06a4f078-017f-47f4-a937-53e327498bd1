name: Docker Release

on:
  release:
    types: [ "published" ]

env:
  DOCKER_IMAGE: |
    blotout/mcp-service:${{ github.ref_name }}

jobs:

  build-and-deploy:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push docker image
      uses: docker/build-push-action@v3
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64
        push: true
        tags: ${{ env.DOCKER_IMAGE }}

    - name: Print Docker Image Name
      run: echo "🎉 Docker Image ${{ env.DOCKER_IMAGE }}"
