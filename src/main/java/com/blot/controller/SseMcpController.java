package com.blot.controller;

import com.blot.service.ApplicationService;
import com.blot.service.AthenaService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

@RestController
@RequestMapping("/mcp/sse")
public class SseMcpController {

    private static final Logger logger = LoggerFactory.getLogger(SseMcpController.class);

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private AthenaService athenaService;

    @Autowired
    private ObjectMapper objectMapper;

    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private static final long SSE_TIMEOUT = 10 * 60 * 1000L; // 10 minutes

    @GetMapping(produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter initSession() {
        String sessionId = UUID.randomUUID().toString();
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        emitters.put(sessionId, emitter);

        logger.info("Initializing SSE session: {}", sessionId);

        emitter.onCompletion(() -> {
            logger.info("SSE session completed: {}", sessionId);
            emitters.remove(sessionId);
        });
        emitter.onTimeout(() -> {
            logger.warn("SSE session timed out: {}", sessionId);
            emitters.remove(sessionId);
        });
        emitter.onError((e) -> {
            logger.error("SSE session error for {}: {}", sessionId, e.getMessage());
            emitters.remove(sessionId);
        });

        try {
            emitter.send(SseEmitter.event()
                    .id(sessionId)
                    .name("endpoint")
                    .data("/mcp/sse/message?sessionId=" + sessionId));
        } catch (IOException e) {
            logger.error("Error sending initial SSE event for session {}: {}", sessionId, e.getMessage());
            emitter.completeWithError(e);
        }

        return emitter;
    }

    @GetMapping(value = "/message", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamMessages(@RequestParam String sessionId) {
        logger.info("Opening SSE message stream for session: {}", sessionId);

        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        emitters.put(sessionId, emitter);

        emitter.onCompletion(() -> {
            logger.info("SSE message stream completed: {}", sessionId);
            emitters.remove(sessionId);
        });
        emitter.onTimeout(() -> {
            logger.warn("SSE message stream timed out: {}", sessionId);
            emitters.remove(sessionId);
        });
        emitter.onError((e) -> {
            logger.error("SSE message stream error for {}: {}", sessionId, e.getMessage());
            emitters.remove(sessionId);
        });

        // Keep-alive ping every 15 seconds
        ScheduledFuture<?> keepAliveTask = scheduler.scheduleAtFixedRate(() -> {
            try {
                emitter.send(SseEmitter.event().name("ping").data("keep-alive"));
            } catch (Exception e) {
                logger.error("Keep-alive failed for session {}: {}", sessionId, e.getMessage());
                emitter.completeWithError(e);
            }
        }, 15, 15, TimeUnit.SECONDS);

        emitter.onCompletion(() -> keepAliveTask.cancel(true));
        emitter.onTimeout(() -> keepAliveTask.cancel(true));
        emitter.onError((e) -> keepAliveTask.cancel(true));

        try {
            emitter.send(SseEmitter.event().name("welcome").data("Connected session " + sessionId));
        } catch (IOException e) {
            logger.error("Failed to send welcome message for session {}: {}", sessionId, e.getMessage());
            emitter.completeWithError(e);
        }

        return emitter;
    }

    @PostMapping(
        value = "/message",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.ALL_VALUE
    )
    public ResponseEntity<Map<String, Object>> handleJsonRpc(@RequestParam String sessionId, @RequestBody Map<String, Object> body) {
        String method = (String) body.get("method");
        Object id = body.get("id");

        logger.info("Received JSON-RPC request: method={}, id={}", method, id);

        if ("initialize".equals(method)) {
            logger.info("Handling 'initialize' request");
            return ResponseEntity.ok(Map.of(
                    "jsonrpc", "2.0",
                    "id", id,
                    "result", Map.of("protocolVersion", "2025-06-18")
            ));
        }

        if ("getManifest".equals(method)) {
            logger.info("Handling 'getManifest' request");

            List<Map<String, Object>> tools = List.of(
                    Map.of("name", "get_applications", "description", "Get a list of applications from Blotout", "parameters", Map.of()),
                    Map.of("name", "run_query", "description", "Execute an AWS Athena query with schema and query", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of(
                                    "schema", Map.of("type", "string"),
                                    "query", Map.of("type", "string")
                            ),
                            "required", List.of("schema", "query")
                    )),
                    Map.of("name", "describe_table", "description", "Get columns and data types for a table", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of(
                                    "schema", Map.of("type", "string"),
                                    "table", Map.of("type", "string")
                            ),
                            "required", List.of("schema", "table")
                    )),
                    Map.of("name", "list_tables", "description", "List tables in schema", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of("schema", Map.of("type", "string")),
                            "required", List.of("schema")
                    )),
                    Map.of("name", "get_status", "description", "Get status of Athena query execution", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of("queryExecutionId", Map.of("type", "string")),
                            "required", List.of("queryExecutionId")
                    )),
                    Map.of("name", "get_result", "description", "Get results from a query execution", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of(
                                    "queryExecutionId", Map.of("type", "string"),
                                    "numberOfRows", Map.of("type", "integer")
                            ),
                            "required", List.of("queryExecutionId")
                    )),
                    Map.of("name", "get_query", "description", "Generate a funnel or attribution query", "parameters", Map.of(
                            "type", "object",
                            "properties", Map.of(
                                    "appName", Map.of("type", "string"),
                                    "queryType", Map.of("type", "string"),
                                    "schema", Map.of("type", "string"),
                                    "events", Map.of("type", "string"),
                                    "attributionType", Map.of("type", "string"),
                                    "fromDate", Map.of("type", "string"),
                                    "toDate", Map.of("type", "string")
                            ),
                            "required", List.of("appName", "queryType", "schema")
                    ))
            );

            return ResponseEntity.ok(Map.of(
                    "jsonrpc", "2.0",
                    "id", id,
                    "result", Map.of("tools", tools)
            ));
        }

        if ("execute".equals(method)) {
            logger.info("Handling 'execute' request");

            Map<String, Object> params = (Map<String, Object>) body.get("params");
            String toolName = (String) params.get("toolName");
            Map<String, Object> input = (Map<String, Object>) params.get("input");

            logger.info("Executing tool: {}", toolName);

            try {
                Object result;
                switch (toolName) {
                    case "get_applications" -> result = applicationService.getApplications((String) input.get("token"));
                    case "run_query" -> result = athenaService.runQuery(
                            (String) input.get("schema"),
                            (String) input.get("query"),
                            (String) input.get("token")
                    );
                    case "describe_table" -> result = athenaService.describeTable(
                            (String) input.get("schema"),
                            (String) input.get("table"),
                            (String) input.get("token")
                    );
                    case "list_tables" -> result = athenaService.listTables((String) input.get("schema"),(String) input.get("token"));
                    case "get_status" -> result = athenaService.getStatus((String) input.get("queryExecutionId"), (String) input.get("token"));
                    case "get_result" -> result = athenaService.getResult(
                            (String) input.get("queryExecutionId"),
                            input.get("numberOfRows") != null ? (Integer) input.get("numberOfRows") : null,
                            (String) input.get("token")
                    );
                    case "get_query" -> result = athenaService.getQuery(
                            (String) input.get("appName"),
                            (String) input.get("queryType"),
                            (String) input.get("schema"),
                            (String) input.getOrDefault("events", ""),
                            (String) input.getOrDefault("attributionType", ""),
                            (String) input.getOrDefault("fromDate", ""),
                            (String) input.getOrDefault("toDate", ""),
                            (String) input.get("token")
                    );
                    default -> {
                        logger.warn("Unknown tool name in 'execute': {}", toolName);
                        return ResponseEntity.ok(Map.of(
                                "jsonrpc", "2.0",
                                "id", id,
                                "error", Map.of("message", "Tool not found: " + toolName)
                        ));
                    }
                }

                logger.info("Tool execution result: {}", result);
                return ResponseEntity.ok(Map.of(
                        "jsonrpc", "2.0",
                        "id", id,
                        "result", Map.of("output", objectMapper.writeValueAsString(result))
                ));

            } catch (Exception e) {
                logger.error("Tool execution error: {}", e.getMessage(), e);
                return ResponseEntity.ok(Map.of(
                        "jsonrpc", "2.0",
                        "id", id,
                        "error", Map.of("message", "Tool execution error: " + e.getMessage())
                ));
            }
        }

        logger.warn("Unknown method received: {}", method);
        return ResponseEntity.badRequest().body(Map.of(
                "jsonrpc", "2.0",
                "id", id,
                "error", Map.of("message", "Unknown method: " + method)
        ));
    }

    public void sendEvent(String sessionId, String eventName, Object data) throws IOException {
        SseEmitter emitter = emitters.get(sessionId);
        if (emitter != null) {
            logger.info("Sending SSE event '{}' to session {}", eventName, sessionId);
            emitter.send(SseEmitter.event().name(eventName).data(data));
        } else {
            logger.warn("Emitter not found for sessionId: {}", sessionId);
        }
    }
}