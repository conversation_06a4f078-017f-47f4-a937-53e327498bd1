package com.blot.config;

import com.blot.configuration.Redis;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Configuration
@ConditionalOnClass(RedisConfig.class)
@EnableConfigurationProperties(Redis.class)
public class RedisConfig {

	@Autowired
	private Redis redis;

	@Bean
	public JedisCluster redisCluster() {
		GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
		poolConfig.setMinIdle(redis.getMinIdle());
		poolConfig.setMaxIdle(redis.getMaxIdle());
		poolConfig.setMaxTotal(redis.getMaxTotal());
		poolConfig.setTestOnBorrow(redis.getTestOnBorrow());
		poolConfig.setTestOnReturn(redis.getTestOnReturn());
		poolConfig.setMaxWaitMillis(redis.getMaxWaitMillis());
		poolConfig.setNumTestsPerEvictionRun(redis.getNumTestsPerEvictionRun());
		Set<HostAndPort> nodes = new HashSet<>();
		for (String node : redis.getNodes()) {
			String[] parts = StringUtils.split(node, ":");
			Assert.state(parts.length == 2,
					"redis node shoule be defined as 'host:port', not '" + Arrays.toString(parts) + "'");
			nodes.add(new HostAndPort(parts[0], Integer.valueOf(parts[1])));
		}

		return new JedisCluster(nodes, redis.getConnectionTimeout(), redis.getMaxAttempts());
	}

}
