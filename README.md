# Blotout AWS Athena & Backend API MCP Server

A simple, clean MCP (Model Context Protocol) server for Blotout AWS Athena integration and dashboard-backend service integration. 
Execute SQL queries, discover schemas, and manage query executions through a standardized interface.
Consume APIs from dashboard-backend service application.

## 🚀 Quick Start

## How to run the mcp-service locally

### Requirements

1. Java 17
2. Claude Desktop Client
3. Dashboard Backend Service running locally
4. Maven 3.8.4 or above

### Set up environment variables properly and export them

1. mcp-service is a Spring boot application and expecting proper values configured in [application.properties](https://github.com/blotoutio/dashboard-backend/blob/main/src/main/resources/application.properties)
2. many of those properties are configured through env vars
3. to find proper values for those env vars, you could copy values from blotout dashboard-backend application staging environment
4. export all env vars so that the application could read them during runtime

### Build and run dashboard-backend

1. Run `mvn clean install` to build the dashboard-backend application
2. Start application with `java -jar target/blotout-0.0.1-SNAPSHOT-*UTC.jar`
3. Open Swagger on `http://localhost:8080/swagger-ui/index.html?url=/v3/api-docs`
4. Jacoco reports have been added to measure the code coverage. The final report can be observed at /target/site/index.html

### Build and run mcp-service

1. Run `mvn clean install` to build the mcp-service application
2. To validate if the application will work correctly with Claude, run `java -jar target/mcp-service-0.0.1-SNAPSHOT.jar`. If it is started in terminal with no messages,
    we are good to go.

### Claude Desktop Client Settings

1. Configure mcp service in the claude_desktop_config.json file as below with appropriate values for environment values:
```json
{
    "mcpServers": {
        "blotout-athena-api-mcp-service": {
            "command": "java",
            "args": [
            "-jar",
            "<Absolute path to cloned codebase>/mcp-service/target/mcp-service-0.0.1-SNAPSHOT.jar"
            ],
            "env": {
                "ENV": "",
                "DASHBOARD_BACKEND_API_URL": "",
                "AWS_ACCESS_KEY_ID": "",
                "AWS_SECRET_ACCESS_KEY": "",
                "AWS_DEFAULT_REGION": "",
                "AMAZON_ACCOUNT_ID": "",
                "ATHENA_TIMEOUT": "",
                "ATHENA_DATABASE": "",
                "ATHENA_OUTPUT_BUCKET": "",
                "ATHENA_ACCESS_KEY_ID": "",
                "ATHENA_SECRET_ACCES_KEY": "",
                "ATHENA_REGION": ""
            }
        }
    }
}
```

2. Exit the Claude Client if it was open before configuration changes. Then restart it by command `open -a "Claude"` .
3. If there are any errors in the Claude Desktop client, observe the logs at `/Users/<USER>/Library/Logs/Claude/blotout-athena-api-mcp-service.log` .
4. To confirm, if the mcp service is running and being recognized by Claude Desktop client, click on `=` (pipes button) on UI. You should see the MCP server with
   name `blotout-athena-api-mcp-service` and also should see the operations exposed by this MCP server.

## How to run the mcp-service locally in SSE(server side events) mode

### Docker installed

1. docker build -f docker/Dockerfile -t mcp-service:latest .
2. docker run --env-file <absolute-path-to-env-file> -p 8080:8080 mcp-service:latest

### Run MCP inspector locally

1. <NAME_EMAIL>:modelcontextprotocol/inspector.git
2. cd inspector
3. npm install
4. npm run build
5. npm start
6. npm start opens up MCP inspector in browser window with URL - http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=<TOKEN-VALUE>


### The .env file should have following variables in it

ENV=
DASHBOARD_BACKEND_API_URL=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AMAZON_ACCOUNT_ID=
ATHENA_TIMEOUT=
ATHENA_DATABASE=
ATHENA_OUTPUT_BUCKET=
ATHENA_ACCESS_KEY_ID=
ATHENA_SECRET_ACCES_KEY=
ATHENA_REGION=
REDIS_CLUSTER_NODES=
REDIS_QUERY_TTL=
TOKEN_EXPIRY=
SERVER_PORT=

### claude_desktop_config.json config

1. Please put the following configuration in the claude_desktop_config.json file, the absolute path is 
   /Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json
```json
    {
      "mcpServers": {
        "blotout-athena-api-mcp-service-remote-mcp": {
          "command": "npx",
          "args": ["mcp-remote", "http://<server-ip>:8080/sse", "--allow-http"]
        }
      }
    }
```
