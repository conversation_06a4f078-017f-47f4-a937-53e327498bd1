package com.blot;

import com.blot.service.ApplicationService;
import com.blot.service.AthenaService;
import com.blot.service.AuthService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class McpServiceApplication {

	public static void main(String[] args) {
		try {
			SpringApplication.run(McpServiceApplication.class, args);
		} catch (Exception e) {
			e.printStackTrace(); // this will surface in Claude logs
			System.err.println("Startup error: " + e.getMessage());
		}
	}

	/*@Bean
	public List<ToolCallback> danTools(ApplicationService applicationService, AthenaService athenaService) {
		return Stream.of(
						ToolCallbacks.from(applicationService),
						ToolCallbacks.from(athenaService)
				).flatMap(Stream::of)
				.toList();
	}*/
	@Bean
	public ToolCallbackProvider blotoutTools(ApplicationService applicationService, AthenaService athenaService, AuthService authService) {
		return MethodToolCallbackProvider
				.builder()
				.toolObjects(applicationService, athenaService, authService).build();
	}
}
